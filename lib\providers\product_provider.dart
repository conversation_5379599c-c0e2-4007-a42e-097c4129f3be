import 'dart:io';
import 'package:flutter/material.dart';
import '../models/product.dart';
import '../services/firestore_service.dart';
import '../services/storage_service.dart';

class ProductProvider extends ChangeNotifier {
  List<Product> _products = [];
  bool _isLoading = false;
  String? _errorMessage;

  List<Product> get products => _products;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  void clearError() {
    _setError(null);
  }

  void loadProducts() {
    FirestoreService.getProducts().listen(
      (products) {
        _products = products;
        notifyListeners();
      },
      onError: (error) {
        _setError('Failed to load products: $error');
      },
    );
  }

  Future<bool> addProduct({
    required String title,
    required String description,
    required double price,
    required double weight,
    required File imageFile,
  }) async {
    try {
      _setLoading(true);
      _setError(null);

      // Upload image first
      final String imageUrl = await StorageService.uploadProductImage(imageFile);

      // Create product
      final Product product = Product(
        id: '', // Will be set by Firestore
        title: title,
        description: description,
        price: price,
        weight: weight,
        imageUrl: imageUrl,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await FirestoreService.addProduct(product);
      _setLoading(false);
      return true;
    } catch (e) {
      _setLoading(false);
      _setError('Failed to add product: $e');
      return false;
    }
  }

  Future<bool> updateProduct(Product product, {File? newImageFile}) async {
    try {
      _setLoading(true);
      _setError(null);

      String imageUrl = product.imageUrl;
      
      // Upload new image if provided
      if (newImageFile != null) {
        // Delete old image
        if (product.imageUrl.isNotEmpty) {
          await StorageService.deleteImage(product.imageUrl);
        }
        // Upload new image
        imageUrl = await StorageService.uploadProductImage(newImageFile);
      }

      final updatedProduct = product.copyWith(
        imageUrl: imageUrl,
        updatedAt: DateTime.now(),
      );

      await FirestoreService.updateProduct(updatedProduct);
      _setLoading(false);
      return true;
    } catch (e) {
      _setLoading(false);
      _setError('Failed to update product: $e');
      return false;
    }
  }

  Future<bool> deleteProduct(Product product) async {
    try {
      _setLoading(true);
      _setError(null);

      // Delete image from storage
      if (product.imageUrl.isNotEmpty) {
        await StorageService.deleteImage(product.imageUrl);
      }

      // Delete product from Firestore
      await FirestoreService.deleteProduct(product.id);
      
      _setLoading(false);
      return true;
    } catch (e) {
      _setLoading(false);
      _setError('Failed to delete product: $e');
      return false;
    }
  }
}
