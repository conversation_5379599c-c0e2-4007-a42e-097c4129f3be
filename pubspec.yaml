name: myfriendroze_admin
description: Admin app for managing products and events for MyFriendRoze

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # Firebase
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3
  cloud_firestore: ^4.13.6
  firebase_storage: ^11.5.6
  
  # UI & Navigation
  cupertino_icons: ^1.0.2
  go_router: ^12.1.3
  
  # Image handling
  image_picker: ^1.0.4
  cached_network_image: ^3.3.0
  
  # Form handling
  flutter_form_builder: ^9.1.1
  form_builder_validators: ^9.1.0
  
  # State management
  provider: ^6.1.1
  
  # Utilities
  uuid: ^4.2.1
  intl: ^0.18.1
  
  # Speech to text
  speech_to_text: ^6.6.0
  permission_handler: ^11.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
  
  fonts:
    - family: Roboto
      fonts:
        - asset: fonts/Roboto-Regular.ttf
        - asset: fonts/Roboto-Bold.ttf
          weight: 700
